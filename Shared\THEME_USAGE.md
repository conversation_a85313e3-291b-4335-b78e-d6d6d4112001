# Universal Theme & Language System Usage Guide

This guide shows how to use the CommonComponent theme and language system in different React projects.

## Installation

```bash
npm install @relativity/common-components
```

## Quick Start

### 1. Basic React App Setup (Theme + Language)

```tsx
// App.tsx
import React from 'react';
import {
  UniversalThemeProvider,
  LanguageProvider,
  ThemeToggle,
  LanguageSelector,
  reactThemeConfig,
  reactLanguageConfig,
  commonTranslations
} from '@relativity/common-components';

function App() {
  return (
    <ThemeProvider {...reactThemeConfig}>
      <LanguageProvider
        {...reactLanguageConfig}
        translations={commonTranslations}
      >
        <div className="min-h-screen bg-background text-foreground">
          <header className="p-4 border-b">
            <div className="flex justify-between items-center">
              <h1>My App</h1>
              <div className="flex items-center gap-3">
                <LanguageSelector variant="select" />
                <ThemeToggle variant="select" />
              </div>
            </div>
          </header>
          <main className="p-4">
            {/* Your app content */}
          </main>
        </div>
      </LanguageProvider>
    </ThemeProvider>
  );
}

export default App;
```

### 2. Next.js Setup

```tsx
// pages/_app.tsx or app/layout.tsx
import { 
  UniversalThemeProvider, 
  nextThemeConfig,
  injectThemeVariables 
} from '@relativity/common-components';

// Inject CSS variables (call once)
if (typeof window !== 'undefined') {
  injectThemeVariables();
}

export default function App({ Component, pageProps }) {
  return (
    <UniversalThemeProvider {...nextThemeConfig}>
      <Component {...pageProps} />
    </UniversalThemeProvider>
  );
}
```

### 3. Vite/Create React App Setup

```tsx
// main.tsx or index.tsx
import React from 'react';
import ReactDOM from 'react-dom/client';
import { 
  UniversalThemeProvider, 
  viteThemeConfig,
  injectThemeVariables 
} from '@relativity/common-components';
import App from './App';

// Inject CSS variables
injectThemeVariables();

ReactDOM.createRoot(document.getElementById('root')!).render(
  <React.StrictMode>
    <UniversalThemeProvider {...viteThemeConfig}>
      <App />
    </UniversalThemeProvider>
  </React.StrictMode>
);
```

## Language System

### Language Components

```tsx
import {
  LanguageSelector,
  LanguageToggleButton,
  LanguageSettings,
  LanguageSwitcher,
  LanguageDropdown
} from '@relativity/common-components';

// Select dropdown (default)
<LanguageSelector variant="select" />

// Button group
<LanguageSelector variant="buttons" showNativeNames={true} />

// Card layout for settings
<LanguageSelector variant="cards" showCodes={true} />

// Icon only (cycles languages)
<LanguageSelector variant="icon" />

// Pre-configured components
<LanguageToggleButton />  // Icon only
<LanguageSettings />      // Cards with codes
<LanguageSwitcher />      // Compact buttons
<LanguageDropdown />      // Full dropdown
```

### Using the Language Hook

```tsx
import { useLanguage } from '@relativity/common-components';

function MyComponent() {
  const {
    language,
    setLanguage,
    currentLanguage,
    isRTL,
    t
  } = useLanguage();

  return (
    <div dir={isRTL ? 'rtl' : 'ltr'}>
      <p>{t('common.welcome')}</p>
      <p>Current: {currentLanguage?.name}</p>
      <p>RTL Mode: {isRTL ? 'Yes' : 'No'}</p>

      <button onClick={() => setLanguage('es')}>
        Switch to Spanish
      </button>
    </div>
  );
}
```

## Theme Components

### ThemeToggle Variants

```tsx
import { 
  ThemeToggle, 
  ThemeToggleButton, 
  ThemeSelector, 
  ThemeSwitcher 
} from '@relativity/common-components';

// Select dropdown (default)
<ThemeToggle variant="select" />

// Button group
<ThemeToggle variant="buttons" showLabels={true} />

// Card layout for settings
<ThemeToggle variant="cards" showDescriptions={true} />

// Icon only (cycles themes)
<ThemeToggle variant="icon" />

// Pre-configured components
<ThemeToggleButton />  // Icon only
<ThemeSelector />      // Cards with descriptions
<ThemeSwitcher />      // Compact buttons
```

### Using the Theme Hook

```tsx
import { useUniversalTheme, useThemeAware } from '@relativity/common-components';

function MyComponent() {
  // Basic theme access
  const { theme, setTheme, resolvedTheme } = useUniversalTheme();
  
  // Theme-aware utilities
  const { 
    isDark, 
    isLight, 
    isSystem, 
    toggleTheme, 
    cycleTheme 
  } = useThemeAware();
  
  return (
    <div>
      <p>Current theme: {theme}</p>
      <p>Resolved theme: {resolvedTheme}</p>
      <p>Is dark mode: {isDark}</p>
      
      <button onClick={toggleTheme}>
        Toggle Theme
      </button>
      
      <button onClick={cycleTheme}>
        Cycle Theme
      </button>
    </div>
  );
}
```

## Framework-Specific Configurations

### Tailwind CSS

```tsx
// Use with Tailwind's dark mode
import { UniversalThemeProvider, tailwindThemeConfig } from '@relativity/common-components';

<UniversalThemeProvider {...tailwindThemeConfig}>
  <div className="bg-white dark:bg-gray-900 text-black dark:text-white">
    {/* Your content */}
  </div>
</UniversalThemeProvider>
```

### Material-UI

```tsx
import { UniversalThemeProvider, muiThemeConfig } from '@relativity/common-components';

<UniversalThemeProvider {...muiThemeConfig}>
  {/* MUI components will read data-theme attribute */}
</UniversalThemeProvider>
```

### Chakra UI

```tsx
import { UniversalThemeProvider, chakraThemeConfig } from '@relativity/common-components';

<UniversalThemeProvider {...chakraThemeConfig}>
  {/* Chakra components will read data-theme attribute */}
</UniversalThemeProvider>
```

## Combined Settings Components

```tsx
import {
  AppearanceSettings,
  CompactAppearanceSettings,
  AppearanceDropdown,
  AppearanceToolbar
} from '@relativity/common-components';

// Full settings page
<AppearanceSettings />

// Compact for navbar
<CompactAppearanceSettings />

// Dropdown for limited space
<AppearanceDropdown />

// Toolbar with buttons
<AppearanceToolbar />
```

## Language Configuration

```tsx
import {
  createLanguageConfig,
  LanguageProvider,
  europeanLanguages,
  globalLanguages,
  commonTranslations
} from '@relativity/common-components';

const customLanguageConfig = createLanguageConfig({
  defaultLanguage: 'en',
  languages: europeanLanguages,
  detectBrowserLanguage: true,
});

// With custom translations
const myTranslations = {
  en: { 'app.title': 'My App' },
  es: { 'app.title': 'Mi Aplicación' },
  fr: { 'app.title': 'Mon App' },
};

<LanguageProvider
  {...customLanguageConfig}
  translations={myTranslations}
>
  {/* Your app */}
</LanguageProvider>
```

## Custom Configuration

```tsx
import { createThemeConfig, UniversalThemeProvider } from '@relativity/common-components';

const customConfig = createThemeConfig({
  defaultTheme: 'dark',
  storageKey: 'my-app-theme',
  themes: ['light', 'dark', 'blue', 'green'],
  attribute: 'data-theme',
  value: {
    light: 'light-mode',
    dark: 'dark-mode',
    blue: 'blue-theme',
    green: 'green-theme',
  },
});

<UniversalThemeProvider {...customConfig}>
  {/* Your app */}
</UniversalThemeProvider>
```

## CSS Variables

The theme system provides CSS variables that work with any CSS framework:

```css
/* These variables are automatically injected */
:root {
  --background: 0 0% 100%;
  --foreground: 222.2 84% 4.9%;
  --primary: 222.2 47.4% 11.2%;
  /* ... more variables */
}

.dark {
  --background: 222.2 84% 4.9%;
  --foreground: 210 40% 98%;
  --primary: 210 40% 98%;
  /* ... more variables */
}

/* Use in your CSS */
.my-component {
  background-color: hsl(var(--background));
  color: hsl(var(--foreground));
  border-color: hsl(var(--border));
}
```

## Utilities

```tsx
import { themeUtils, applyTheme } from '@relativity/common-components';

// Check user preferences
const prefersDark = themeUtils.prefersDark();
const prefersReducedMotion = themeUtils.prefersReducedMotion();

// Manual theme application
applyTheme('dark', { storageKey: 'my-theme' });

// Storage utilities
const storedTheme = themeUtils.getStoredTheme('my-theme');
themeUtils.setStoredTheme('dark', 'my-theme');
themeUtils.removeStoredTheme('my-theme');
```

## TypeScript Support

All components and hooks are fully typed:

```tsx
import type { 
  Theme, 
  ThemeConfig, 
  ThemeProviderState 
} from '@relativity/common-components';

const theme: Theme = 'dark';
const config: ThemeConfig = { defaultTheme: 'system' };
```

## Best Practices

1. **SSR/SSG**: Use `disableTransitionOnChange: true` for Next.js to prevent flash
2. **Performance**: The theme provider uses minimal re-renders
3. **Accessibility**: System theme detection respects user preferences
4. **Storage**: Gracefully handles localStorage errors
5. **Flexibility**: Works with any CSS framework or custom styles

## Migration from next-themes

If you're migrating from `next-themes`, the API is very similar:

```tsx
// Before (next-themes)
import { ThemeProvider, useTheme } from 'next-themes';

// After (CommonComponent)
import { UniversalThemeProvider, useUniversalTheme } from '@relativity/common-components';
```

The main differences:
- `UniversalThemeProvider` instead of `ThemeProvider`
- `useUniversalTheme` instead of `useTheme`
- Additional utilities like `useThemeAware`
- More configuration options
- Framework-agnostic design
