import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter, } from "../ui/dialog";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Textarea } from "../ui/textarea";
import { Checkbox } from "../ui/checkbox";
import { Card, CardContent, CardHeader, CardTitle } from "../ui/card";
import { Save, X } from "lucide-react";
const AddEditRoleModal = ({ open, onClose, role, setRole, isEdit, onSave, availablePermissions, }) => {
    const groupedPermissions = availablePermissions.reduce((acc, permission) => {
        if (!acc[permission.resource])
            acc[permission.resource] = [];
        acc[permission.resource].push(permission);
        return acc;
    }, {});
    const handlePermissionToggle = (permissionId) => {
        const currentPermissions = role.permissionsList || [];
        const exists = currentPermissions.some((p) => p._id === permissionId);
        let newPermissionsList;
        if (exists) {
            // Remove the permission object
            newPermissionsList = currentPermissions.filter((p) => p._id !== permissionId);
        }
        else {
            // Add the permission object
            const permissionToAdd = availablePermissions.find((p) => p._id === permissionId);
            if (permissionToAdd) {
                newPermissionsList = [...currentPermissions, permissionToAdd];
            }
            else {
                newPermissionsList = currentPermissions;
            }
        }
        setRole({ ...role, permissionsList: newPermissionsList });
    };
    const handleSave = () => {
        onSave();
    };
    return (_jsx(Dialog, { open: open, onOpenChange: onClose, children: _jsx(DialogContent, { className: "p-0 gap-0 overflow-hidden", style: {
                maxWidth: "90vw",
                width: "70%",
                maxHeight: "90vh",
                height: "100%",
                minHeight: "600px",
            }, children: _jsxs("div", { style: { display: "flex", flexDirection: "column", height: "100%" }, children: [_jsx(DialogHeader, { className: "px-6 py-4 border-b", style: { flexShrink: 0 }, children: _jsx(DialogTitle, { className: "text-lg font-semibold", children: isEdit ? "Edit Role" : "Create Role" }) }), _jsx("div", { className: "px-6 py-4", style: {
                            flex: 1,
                            overflowY: "auto",
                            //minHeight: 0,
                            //maxHeight: 'calc(90vh - 140px)'
                        }, children: _jsxs("div", { style: { display: "flex", flexDirection: "column", gap: "24px" }, children: [_jsxs("div", { children: [_jsx(Label, { htmlFor: "roleName", className: "text-sm font-medium", children: "Role Name" }), _jsx(Input, { id: "roleName", value: role.name || "", onChange: (e) => setRole({ ...role, name: e.target.value }), placeholder: "Enter role name", className: "mt-1" })] }), _jsxs("div", { children: [_jsx(Label, { htmlFor: "roleDescription", className: "text-sm font-medium", children: "Description" }), _jsx(Textarea, { id: "roleDescription", value: role.description || "", onChange: (e) => setRole({ ...role, description: e.target.value }), placeholder: "Enter role description", rows: 2, className: "mt-1" })] }), _jsxs("div", { children: [_jsx("h3", { className: "text-lg font-semibold mb-4", children: "Permissions" }), _jsx("div", { style: {
                                                maxHeight: "300px",
                                                overflowY: "auto",
                                                paddingRight: "8px",
                                                display: "flex",
                                                flexDirection: "column",
                                                gap: "16px",
                                            }, children: Object.entries(groupedPermissions).map(([resource, perms]) => (_jsxs(Card, { className: "border shadow-sm", children: [_jsx(CardHeader, { className: "pb-3 pt-4 px-5", children: _jsx(CardTitle, { className: "text-base font-semibold capitalize", children: resource }) }), _jsx(CardContent, { className: "pt-0 px-5 pb-4", children: _jsx("div", { style: {
                                                                display: "grid",
                                                                gridTemplateColumns: "repeat(auto-fit, minmax(250px, 1fr))",
                                                                gap: "12px",
                                                            }, children: perms?.map((perm) => (_jsxs("div", { className: "flex items-start space-x-3 py-2 px-2 rounded hover:bg-background-50", style: { minHeight: "40px" }, children: [_jsx(Checkbox, { id: perm._id, checked: role.permissionsList?.some((p) => p._id === perm._id) || false, onCheckedChange: () => handlePermissionToggle(perm._id), className: "mt-1 flex-shrink-0" }), _jsx(Label, { htmlFor: perm._id, className: "text-sm font-normal cursor-pointer leading-relaxed break-words", style: { wordBreak: "break-word" }, children: perm.description })] }, perm._id))) }) })] }, resource))) })] })] }) }), _jsx(DialogFooter, { className: "px-6 py-4 border-t bg-background-50", style: {
                            flexShrink: 0,
                            position: "sticky",
                            bottom: 0,
                            zIndex: 10,
                        }, children: _jsxs("div", { className: "flex gap-3 w-full justify-end", children: [_jsxs(Button, { variant: "outline", onClick: onClose, className: "min-w-[100px]", children: [_jsx(X, { className: "w-4 h-4 mr-2" }), "Cancel"] }), _jsxs(Button, { onClick: handleSave, className: "min-w-[120px]", children: [_jsx(Save, { className: "w-4 h-4 mr-2" }), isEdit ? "Update Role" : "Create Role"] })] }) })] }) }) }));
};
export default AddEditRoleModal;
