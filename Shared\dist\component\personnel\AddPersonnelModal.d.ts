import React from "react";
import "react-phone-input-2/lib/style.css";
import { type Person } from "../../lib/Schemas/Personnel";
interface AddPersonnelModalProps {
    open: boolean;
    onOpenChange: (open: boolean) => void;
    mode: "add" | "edit";
    person?: Person | null;
    licenseInfo?: Record<string, any>;
    selectedOrganizationId?: string;
    appName?: string;
    orgApiUrl?: string;
    permissionList?: any[];
}
declare const AddPersonnelModal: React.FC<AddPersonnelModalProps>;
export default AddPersonnelModal;
