import { jsx as _jsx, jsxs as _jsxs, Fragment as _Fragment } from "react/jsx-runtime";
import { useEffect, useState } from "react";
import { Dialog, DialogContent, DialogHeader, DialogTitle } from "../ui/dialog";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue, } from "../ui/select";
import { Alert, AlertDescription } from "../ui/alert";
import { Loader2, ChevronDown, AlertTriangle } from "lucide-react";
import PhoneInput from "react-phone-input-2";
import "react-phone-input-2/lib/style.css";
import { useCreatePersonnel, useUpdatePersonnel, useRoles, } from "../../hooks/usePersonnel";
import { useOrganizations } from "../../hooks/useOrganizations";
import { ApplicationName, } from "../../lib/Schemas/Personnel";
import { Popover, PopoverContent, PopoverTrigger } from "../ui/popover";
import { useOrgRolesByOrganization } from "../../hooks/useMaster";
import { useDecodedJwt } from "../../hooks/useDecodedJwt";
import { Checkbox } from "../ui";
const AddPersonnelModal = ({ open, onOpenChange, mode, person, licenseInfo = {}, selectedOrganizationId, appName, orgApiUrl, permissionList, }) => {
    const isEditMode = mode === "edit";
    const RELATIVITY_ADMIN_ROLE_NAME = "Relativity Admin";
    const ORGANIZATION_ADMIN_ROLE_NAME = "Organization Admin";
    const [formData, setFormData] = useState({
        firstName: "",
        lastName: "",
        email: "",
        phone: "",
        organizationId: "",
        selectedRoles: [],
        allowedApplications: [],
        selectedPermissions: [],
    });
    // Add state for validation errors
    const [validationErrors, setValidationErrors] = useState([]);
    const [showValidationAlert, setShowValidationAlert] = useState(false);
    const createPersonnel = useCreatePersonnel();
    const updatePersonnel = useUpdatePersonnel();
    const { data: apiOrganizations } = useOrganizations();
    const jwtPayload = useDecodedJwt();
    const rolesList = jwtPayload?.roles || [];
    const hasAdminRole = rolesList?.some((role) => role.name === RELATIVITY_ADMIN_ROLE_NAME ||
        role.name === ORGANIZATION_ADMIN_ROLE_NAME);
    const { data: apiRoles } = appName === ApplicationName.ORGANIZATION_ADMIN
        ? useOrgRolesByOrganization(selectedOrganizationId, orgApiUrl)
        : useRoles();
    const roles = Array.isArray(apiRoles) ? apiRoles : [];
    //  Final roles to show in UI
    const availableRoles = hasAdminRole
        ? roles // Show all roles from API
        : roles?.filter((role) => rolesList?.some((r) => r.id === role.id)); // Filter only roles user has
    //  Optionally filter out Relativity Admin if app is Organization Admin (UI rule)
    const filteredRoles = availableRoles?.filter((role) => {
        if (appName === ApplicationName.ORGANIZATION_ADMIN &&
            role.name === RELATIVITY_ADMIN_ROLE_NAME) {
            return false;
        }
        return true;
    });
    const organizations = Array.isArray(apiOrganizations)
        ? apiOrganizations.map((org) => ({
            id: org.id ?? org._id,
            name: org.organizationName,
        }))
        : [];
    // Check if organization dropdown should be disabled (when appName is OrganizationAdmin)
    const isOrganizationDropdownDisabled = appName === ApplicationName.ORGANIZATION_ADMIN;
    // Check if Application dropdown should be visible (only for organizationadmin app)
    const shouldShowApplicationDropdown = appName === ApplicationName.ORGANIZATION_ADMIN;
    const selectedOrganization = apiOrganizations?.find((org) => org.id === formData.organizationId || org._id === formData.organizationId);
    const availableApplications = selectedOrganization?.selectedAppLicenses || [];
    const enhancedApplications = [...availableApplications];
    if (!availableApplications.some((app) => app.applicationName === "organizationadmin")) {
        enhancedApplications.push({
            _id: "org-admin",
            applicationName: "organizationadmin",
        });
    }
    // Get license info for selected organization
    const selectedOrgName = selectedOrganization?.organizationName;
    const currentOrgLicenseInfo = selectedOrgName
        ? licenseInfo[selectedOrgName]
        : null;
    // Determine which special role is selected (if any)
    const currentRoleNames = formData.selectedRoles.map((r) => r.name);
    const isRelativityAdmin = currentRoleNames.includes(RELATIVITY_ADMIN_ROLE_NAME);
    // Check if selected applications have available licenses
    const getApplicationLicenseStatus = (appName) => {
        if (!currentOrgLicenseInfo?.applications)
            return null;
        const status = currentOrgLicenseInfo.applications[appName] || null;
        return status;
    };
    // Validate license availability
    const validateLicenses = () => {
        const errors = [];
        // Only validate for add mode
        if (isEditMode)
            return { isValid: true, errors: [] };
        // If no license info available, allow (fail-safe)
        if (!currentOrgLicenseInfo?.applications) {
            return { isValid: true, errors: [] };
        }
        const selectedApps = formData.allowedApplications || [];
        // Check each selected application
        selectedApps.forEach((appName) => {
            const licenseStatus = getApplicationLicenseStatus(appName);
            if (licenseStatus && licenseStatus.availableLicenses <= 0) {
                errors.push(`Cannot add user: No licenses available for "${appName}" application (${licenseStatus.usedLicenses}/${licenseStatus.totalLicenses} used)`);
            }
        });
        return {
            isValid: errors.length === 0,
            errors: errors,
        };
    };
    const getLicenseErrors = () => {
        // Only show errors for add mode
        if (isEditMode)
            return [];
        const validation = validateLicenses();
        return validation.errors;
    };
    // Clear validation errors when form data changes
    useEffect(() => {
        if (showValidationAlert) {
            setShowValidationAlert(false);
            setValidationErrors([]);
        }
    }, [formData.allowedApplications, formData.organizationId]);
    // Pre-select organization when selectedOrganizationId is provided and dropdown is disabled
    useEffect(() => {
        if (isOrganizationDropdownDisabled &&
            selectedOrganizationId &&
            !isEditMode &&
            organizations.length > 0) {
            // Find organization by matching with selectedOrganizationId
            const selectedOrganization = organizations.find((org) => org.id === selectedOrganizationId ||
                org.id === selectedOrganizationId?.toString());
            if (selectedOrganization) {
                setFormData((prev) => ({
                    ...prev,
                    organizationId: selectedOrganization.id,
                }));
            }
        }
    }, [
        isOrganizationDropdownDisabled,
        selectedOrganizationId,
        isEditMode,
        organizations,
    ]);
    // In edit mode, set formData from person prop
    useEffect(() => {
        if (isEditMode && person && roles.length > 0) {
            // Convert person.roleId to array format and ensure all role IDs exist in current roles
            let personRoleIds = [];
            if (Array.isArray(person.roles)) {
                // Handle both string IDs and role objects
                personRoleIds = person.roles
                    .map((roleItem) => {
                    // If it's already a string ID
                    if (typeof roleItem === "string") {
                        const exists = roles.some((role) => role.id === roleItem || role._id === roleItem);
                        return exists ? roleItem : null;
                    }
                    // If it's a role object
                    else if (typeof roleItem === "object" && roleItem.id) {
                        const exists = roles.some((role) => role.id === roleItem.id || role._id === roleItem.id);
                        return exists ? roleItem.id : null;
                    }
                    return null;
                })
                    .filter((id) => id !== null);
            }
            else {
                // Handle legacy data where roleId might be a single string
                // This is for backward compatibility
                const roleIdAsString = person.roles;
                if (typeof roleIdAsString === "string" && roleIdAsString) {
                    const exists = roles.some((role) => role.id === roleIdAsString || role._id === roleIdAsString);
                    if (exists) {
                        personRoleIds = [roleIdAsString];
                    }
                }
            }
            // If no roles found by ID, try to match by role name (fallback)
            if (personRoleIds.length === 0 && person.roleName) {
                const roleByName = roles.find((role) => role.name === person.roleName);
                if (roleByName) {
                    personRoleIds = [roleByName.id];
                }
            }
            // Get role objects for the selected role IDs
            const selectedRoleObjects = roles.filter((role) => personRoleIds.includes(role.id));
            const personRoleNames = selectedRoleObjects.map((role) => role.name);
            // Set allowed applications based on roles
            let allowedApps = person.allowedApplications || [];
            if (personRoleNames.includes(RELATIVITY_ADMIN_ROLE_NAME)) {
                allowedApps = [
                    ApplicationName.RELATIVITY_ADMIN,
                    ApplicationName.ORGANIZATION_ADMIN,
                ];
            }
            else if (personRoleNames.includes(ORGANIZATION_ADMIN_ROLE_NAME)) {
                allowedApps = [ApplicationName.ORGANIZATION_ADMIN];
            }
            setFormData({
                id: person.id,
                firstName: person.firstName,
                lastName: person.lastName,
                email: person.email,
                phone: person.phone,
                organizationId: person.organizationId,
                selectedRoles: selectedRoleObjects,
                hasLogin: person.hasLogin,
                status: person.status,
                allowedApplications: allowedApps,
                isSystemUser: person.isSystemUser,
                selectedPermissions: person.selectedPermissions || [],
            });
            // Force a small delay to ensure state is updated before rendering
            setTimeout(() => { }, 100);
        }
        else if (!isEditMode) {
            setFormData({
                firstName: "",
                lastName: "",
                email: "",
                phone: "",
                organizationId: "",
                selectedRoles: [],
                allowedApplications: [],
                selectedPermissions: [],
            });
        }
        // Clear validation errors when modal opens/closes
        setShowValidationAlert(false);
        setValidationErrors([]);
    }, [isEditMode, person, open, roles]);
    const handleSubmit = async () => {
        // Clear previous validation errors
        setValidationErrors([]);
        setShowValidationAlert(false);
        // Validate required fields
        const requiredFieldErrors = [];
        if (!formData.firstName?.trim())
            requiredFieldErrors.push("First Name is required");
        if (!formData.lastName?.trim())
            requiredFieldErrors.push("Last Name is required");
        if (!formData.email?.trim())
            requiredFieldErrors.push("Email is required");
        if (!formData.organizationId?.trim())
            requiredFieldErrors.push("Organization is required");
        // Validate role selection based on appName
        if (!formData.selectedRoles || formData.selectedRoles.length === 0) {
            requiredFieldErrors.push("At least one role must be selected");
        }
        else if (appName === ApplicationName.RELATIVITY_ADMIN &&
            formData.selectedRoles.length > 1) {
            requiredFieldErrors.push("Only one role can be selected for Relativity Admin application");
        }
        // Validate email format
        if (formData.email && !/^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(formData.email)) {
            requiredFieldErrors.push("Please enter a valid email address");
        }
        if (requiredFieldErrors.length > 0) {
            setValidationErrors(requiredFieldErrors);
            setShowValidationAlert(true);
            return;
        }
        // Check license availability for add mode
        if (!isEditMode) {
            const licenseValidation = validateLicenses();
            if (!licenseValidation.isValid) {
                setValidationErrors(licenseValidation.errors);
                setShowValidationAlert(true);
                console.error("License validation failed:", licenseValidation.errors);
                return;
            }
        }
        try {
            // Additional validation for organizationId
            if (!formData.organizationId?.trim()) {
                console.error("❌ Organization ID is missing or empty");
                setValidationErrors(["Organization must be selected"]);
                setShowValidationAlert(true);
                return;
            }
            if (isEditMode && formData.id) {
                const updateData = {
                    ...formData,
                    id: formData.id,
                    firstName: formData.firstName,
                    lastName: formData.lastName,
                    email: formData.email,
                    phone: formData.phone || "",
                    organizationId: formData.organizationId,
                    roles: formData.selectedRoles || [],
                    allowedApplications: formData.allowedApplications || [],
                    selectedPermissions: formData.selectedPermissions || [],
                    status: formData.status ?? "Active",
                    isSystemUser: formData.isSystemUser ?? false,
                };
                await updatePersonnel.mutateAsync(updateData);
            }
            else {
                const createData = {
                    firstName: formData.firstName,
                    lastName: formData.lastName,
                    email: formData.email,
                    phone: formData.phone || "",
                    organizationId: formData.organizationId,
                    roles: formData.selectedRoles || [],
                    allowedApplications: formData.allowedApplications || [],
                    selectedPermissions: formData.selectedPermissions || [],
                    status: "Active",
                    isSystemUser: ApplicationName.RELATIVITY_ADMIN === appName ? true : false,
                };
                await createPersonnel.mutateAsync(createData);
            }
            onOpenChange(false);
        }
        catch (error) {
            // Extract more detailed error information
            let errorMessage = "Failed to save personnel. Please try again.";
            if (error?.response?.data?.message) {
                errorMessage = error.response.data.message;
            }
            else if (error?.response?.data?.error) {
                errorMessage = error.response.data.error;
            }
            else if (error?.message) {
                errorMessage = error.message;
            }
            // Handle validation errors from backend
            if (error?.response?.data?.errors &&
                Array.isArray(error.response.data.errors)) {
                setValidationErrors(error.response.data.errors);
            }
            else {
                setValidationErrors([errorMessage]);
            }
            setShowValidationAlert(true);
        }
    };
    const toggleApplication = (appName) => {
        const current = formData.allowedApplications || [];
        if (current.includes(appName)) {
            setFormData({
                ...formData,
                allowedApplications: current.filter((name) => name !== appName),
            });
        }
        else {
            setFormData({
                ...formData,
                allowedApplications: [...current, appName],
            });
        }
    };
    const togglePermission = (permission) => {
        const current = formData.selectedPermissions || [];
        const exists = current.some((p) => p._id === permission._id);
        if (exists) {
            setFormData({
                ...formData,
                selectedPermissions: current.filter((p) => p._id !== permission._id),
            });
        }
        else {
            setFormData({
                ...formData,
                selectedPermissions: [...current, permission],
            });
        }
    };
    const licenseErrors = getLicenseErrors();
    const allValidationErrors = showValidationAlert
        ? validationErrors
        : licenseErrors;
    return (_jsx(Dialog, { open: open, onOpenChange: onOpenChange, children: _jsxs(DialogContent, { className: "p-0 gap-0 overflow-hidden", style: {
                maxWidth: "70vw",
                width: "70%",
                // maxHeight: "80vh",
                height: "80%",
                minHeight: "600px",
                overflow: "auto",
            }, children: [_jsx(DialogHeader, { children: _jsx(DialogTitle, { children: isEditMode ? "Edit Personnel" : "Add New Personnel" }) }), allValidationErrors.length > 0 && (_jsxs(Alert, { variant: "destructive", children: [_jsx(AlertTriangle, { className: "h-4 w-4" }), _jsxs(AlertDescription, { children: [_jsx("div", { className: "font-medium", children: showValidationAlert
                                        ? "Validation Error"
                                        : "License Limit Exceeded" }), _jsx("ul", { className: "mt-1 text-sm list-disc list-inside", children: allValidationErrors.map((error, index) => (_jsx("li", { children: error }, index))) })] })] })), _jsxs("div", { className: "space-y-4", children: [_jsxs("div", { className: "grid grid-cols-1 sm:grid-cols-2 gap-4", children: [_jsxs("div", { children: [_jsx(Label, { htmlFor: "firstName", children: "First Name" }), _jsx(Input, { id: "firstName", type: "text", placeholder: "First Name", value: formData.firstName || "", onChange: (e) => setFormData({ ...formData, firstName: e.target.value }), required: true })] }), _jsxs("div", { children: [_jsx(Label, { htmlFor: "lastName", children: "Last Name" }), _jsx(Input, { id: "lastName", type: "text", placeholder: "Last Name", value: formData.lastName || "", onChange: (e) => setFormData({ ...formData, lastName: e.target.value }), required: true })] })] }), _jsxs("div", { children: [_jsx(Label, { htmlFor: "email", children: "Email" }), _jsx(Input, { id: "email", type: "email", placeholder: "<EMAIL>", value: formData.email || "", onChange: (e) => setFormData({ ...formData, email: e.target.value }), required: true })] }), _jsxs("div", { children: [_jsx(Label, { htmlFor: "phone", children: "Phone Number" }), _jsx(PhoneInput, { country: "us", value: formData.phone || "", onChange: (value) => setFormData({ ...formData, phone: value }), inputStyle: {
                                        width: "100%",
                                        height: "2.5rem",
                                        borderRadius: "0.375rem",
                                        border: "1px solid #E5E7EB",
                                        paddingLeft: "3rem",
                                    }, containerStyle: { width: "100%" } })] }), _jsxs("div", { children: [_jsxs(Label, { htmlFor: "organization", children: ["Organization", isOrganizationDropdownDisabled && (_jsxs("span", { className: "text-sm text-500 ml-2", children: ["(Auto-selected for ", ApplicationName.ORGANIZATION_ADMIN, ")"] }))] }), _jsxs(Select, { value: formData.organizationId || "", onValueChange: (value) => setFormData({
                                        ...formData,
                                        organizationId: value,
                                        allowedApplications: [],
                                        selectedPermissions: [],
                                    }), disabled: isOrganizationDropdownDisabled, children: [_jsx(SelectTrigger, { className: isOrganizationDropdownDisabled
                                                ? "opacity-50 cursor-not-allowed"
                                                : "", children: _jsx(SelectValue, { placeholder: "Select organization" }) }), _jsx(SelectContent, { children: organizations.map((org) => (_jsx(SelectItem, { value: org.id, children: org.name }, org.id))) })] })] }), _jsxs("div", { children: [_jsx(Label, { htmlFor: "role", children: appName === ApplicationName.RELATIVITY_ADMIN
                                        ? "Role (Select One)"
                                        : "Roles (Select Multiple)" }), _jsxs("div", { className: "space-y-2 max-h-48 overflow-y-auto border rounded p-3", style: { maxHeight: "200px" }, children: [filteredRoles.map((role) => {
                                            const isChecked = formData.selectedRoles.some((r) => r.id === (role?._id?.toString?.() || role.id));
                                            // For Relativity Admin app, use radio buttons (single selection)
                                            if (appName === ApplicationName.RELATIVITY_ADMIN) {
                                                return (_jsxs("label", { className: "flex items-center space-x-2 cursor-pointer hover:bg-background-50 p-2 rounded", children: [_jsx("input", { type: "radio", name: "roleSelection", checked: isChecked, onChange: (e) => {
                                                                if (e.target.checked) {
                                                                    // For single selection, replace the entire array with just this role object
                                                                    const selectedRoles = [
                                                                        {
                                                                            id: role?._id?.toString() || role?.id,
                                                                            name: role?.name || "",
                                                                            description: role?.description || "",
                                                                            permissionsList: role?.permissionsList || [],
                                                                        },
                                                                    ];
                                                                    // Calculate allowed applications based on selected role
                                                                    const selectedRoleNames = selectedRoles.map((r) => r.name);
                                                                    let allowedApps = [];
                                                                    if (selectedRoleNames.includes(RELATIVITY_ADMIN_ROLE_NAME)) {
                                                                        allowedApps = [
                                                                            ApplicationName.RELATIVITY_ADMIN,
                                                                            ApplicationName.ORGANIZATION_ADMIN,
                                                                        ];
                                                                    }
                                                                    else if (selectedRoleNames.includes(ORGANIZATION_ADMIN_ROLE_NAME)) {
                                                                        allowedApps = [
                                                                            ApplicationName.ORGANIZATION_ADMIN,
                                                                        ];
                                                                    }
                                                                    else {
                                                                        allowedApps = [];
                                                                    }
                                                                    setFormData({
                                                                        ...formData,
                                                                        selectedRoles: selectedRoles,
                                                                        allowedApplications: allowedApps,
                                                                    });
                                                                }
                                                            }, className: "accent-blue-500" }), _jsx("span", { className: "text-sm font-medium", style: { marginLeft: "2px" }, children: role.name })] }, role.id));
                                            }
                                            // For Organization Admin app, use checkboxes (multiple selection)
                                            return (_jsxs("label", { className: "flex items-center space-x-2 cursor-pointer hover:bg-background-50 p-2 rounded", children: [_jsx("input", { type: "checkbox", checked: isChecked, onChange: (e) => {
                                                            let selectedRoles = [...formData.selectedRoles];
                                                            if (e.target.checked) {
                                                                // Add role object if not already selected
                                                                if (!selectedRoles.some((r) => r.id === role.id)) {
                                                                    let newRole = {
                                                                        id: role?._id?.toString() || role?.id,
                                                                        name: role?.name || "",
                                                                        description: role?.description || "",
                                                                        permissionsList: role?.permissionsList || [],
                                                                    };
                                                                    selectedRoles.push(newRole);
                                                                }
                                                            }
                                                            else {
                                                                // Remove role object
                                                                selectedRoles = selectedRoles.filter((r) => r.id !== role.id);
                                                            }
                                                            setFormData({
                                                                ...formData,
                                                                selectedRoles: selectedRoles,
                                                            });
                                                        }, className: "accent-blue-500" }), _jsx("span", { className: "text-sm font-medium", style: { marginLeft: "2px" }, children: role.name })] }, `${role.id}-${isChecked}`));
                                        }), filteredRoles.length === 0 && (_jsx("div", { className: "text-500 text-sm text-center py-4", children: "No roles available" }))] }, `roles-${formData.selectedRoles
                                    .map((r) => r.id)
                                    .join("-")}`), formData.selectedRoles.length > 0 && (_jsxs("div", { className: "mt-2 text-sm text-600", children: ["Selected (", formData.selectedRoles.length, "):", " ", formData.selectedRoles.map((r) => r.name).join(", "), appName === ApplicationName.RELATIVITY_ADMIN &&
                                            formData.selectedRoles.length > 1 && (_jsx("span", { className: "text-red-500 ml-2", children: "(Only one role allowed for Relativity Admin)" }))] }))] }), shouldShowApplicationDropdown && (_jsxs("div", { children: [_jsx(Label, { htmlFor: "applications", children: "Applications" }), _jsxs(Popover, { children: [_jsx(PopoverTrigger, { asChild: true, children: _jsxs(Button, { type: "button", variant: "outline", className: "w-full justify-between", disabled: isRelativityAdmin || enhancedApplications.length === 0, children: [formData.allowedApplications.length > 0
                                                        ? `${formData.allowedApplications.length} application(s) selected`
                                                        : "Select applications", _jsx(ChevronDown, { className: "h-4 w-4 ml-2" })] }) }), _jsx(PopoverContent, { className: "w-80 p-2", children: _jsx("div", { className: "space-y-2 max-h-64 overflow-auto", children: enhancedApplications.length === 0 ? (_jsx("span", { className: "text-400", children: "No applications available" })) : (enhancedApplications.map((app) => {
                                                    const licenseStatus = getApplicationLicenseStatus(app.applicationName);
                                                    const used = licenseStatus?.usedLicenses ?? 0;
                                                    const total = licenseStatus?.totalLicenses ?? 0;
                                                    const usedPercent = total > 0 ? (used / total) * 100 : 0;
                                                    const isSelected = formData?.allowedApplications?.includes(app.applicationName);
                                                    const isDisabled = isRelativityAdmin ||
                                                        (licenseStatus?.availableLicenses <= 0 &&
                                                            !isSelected);
                                                    return (_jsxs("div", { className: "p-2 border rounded space-y-1", children: [_jsxs("div", { className: "flex items-center justify-between", children: [_jsxs("label", { className: "flex items-center space-x-2 cursor-pointer", children: [_jsx("input", { type: "checkbox", checked: isSelected, onChange: () => toggleApplication(app.applicationName), className: "accent-blue-500", disabled: isDisabled }), _jsx("span", { className: isDisabled ? "text-400" : "", children: app.applicationName })] }), _jsx("span", { className: "text-xs text-muted-foreground", children: `available: ${Math.max((total ?? 0) - (used ?? 0), 0)} | used: ${used ?? 0} | total: ${total ?? 0}` })] }), _jsx("div", { className: "h-2 w-full bg-background-200 rounded overflow-hidden", children: _jsx("div", { className: `h-full ${usedPercent >= 100
                                                                        ? "bg-red-500"
                                                                        : "bg-blue-500"}`, style: { width: `${usedPercent}%` } }) })] }, app._id));
                                                })) }) })] })] })), shouldShowApplicationDropdown && (_jsxs("div", { children: [_jsx(Label, { htmlFor: "permissions", children: "Select Permissions" }), _jsx("div", { className: "border rounded p-2 max-h-48 overflow-y-auto", children: _jsx("div", { className: "grid grid-cols-3 gap-2", children: permissionList?.map((perm) => {
                                            const isSelected = formData.selectedPermissions.some((p) => p._id === perm._id);
                                            return (_jsxs("div", { className: "flex items-center gap-2", children: [_jsx(Checkbox, { checked: isSelected, onCheckedChange: () => togglePermission(perm) }), _jsx("label", { className: "text-sm", children: perm.description })] }, perm._id));
                                        }) }) })] })), _jsxs("div", { className: "flex justify-end space-x-2 pt-4", children: [_jsx(Button, { type: "button", variant: "outline", onClick: () => onOpenChange(false), children: "Cancel" }), _jsx(Button, { type: "submit", disabled: createPersonnel.isPending || updatePersonnel.isPending, onClick: handleSubmit, children: createPersonnel.isPending || updatePersonnel.isPending ? (_jsxs(_Fragment, { children: [_jsx(Loader2, { className: "mr-2 h-4 w-4 animate-spin" }), isEditMode ? "Saving..." : "Adding..."] })) : isEditMode ? ("Save Changes") : ("Add Personnel") })] })] })] }) }));
};
export default AddPersonnelModal;
