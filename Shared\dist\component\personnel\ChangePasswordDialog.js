import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState } from 'react';
import { <PERSON><PERSON>, DialogContent, DialogHeader, DialogTitle, DialogFooter } from '../ui/dialog';
import { Input } from '../ui/input';
import { Button } from '../ui/button';
import { Eye, EyeOff, X } from 'lucide-react';
const ResetPasswordDialog = ({ open, onOpenChange, onSubmit }) => {
    const [currentPassword, setCurrentPassword] = useState('');
    const [password, setPassword] = useState('');
    const [confirmPassword, setConfirmPassword] = useState('');
    const [error, setError] = useState('');
    // Password visibility states
    const [showPassword, setShowPassword] = useState(false);
    const [showConfirmPassword, setShowConfirmPassword] = useState(false);
    const handleSubmit = () => {
        if (password !== confirmPassword) {
            setError("New and confirm passwords don't match");
            return;
        }
        setError('');
        onSubmit({ currentPassword, password });
        // Reset form and visibility states
        setCurrentPassword('');
        setPassword('');
        setConfirmPassword('');
        setShowPassword(false);
        setShowConfirmPassword(false);
        onOpenChange(false);
    };
    const handleDialogClose = (open) => {
        if (!open) {
            // Reset form and visibility states when dialog closes
            setCurrentPassword('');
            setPassword('');
            setConfirmPassword('');
            setShowPassword(false);
            setShowConfirmPassword(false);
            setError('');
        }
        onOpenChange(open);
    };
    return (_jsx(Dialog, { open: open, onOpenChange: handleDialogClose, children: _jsxs(DialogContent, { children: [_jsx(DialogHeader, { children: _jsxs("div", { className: "flex items-center justify-between", children: [_jsx(DialogTitle, { children: "Change Password" }), _jsxs("button", { type: "button", className: "rounded-sm opacity-70 ring-offset-background transition-opacity hover:opacity-100 focus:outline-none focus:ring-2 focus:ring-ring focus:ring-offset-2 disabled:pointer-events-none data-[state=open]:bg-accent data-[state=open]:text-muted-foreground", onClick: () => handleDialogClose(false), children: [_jsx(X, { className: "h-4 w-4" }), _jsx("span", { className: "sr-only", children: "Close" })] })] }) }), _jsxs("div", { className: "space-y-4 mt-3", children: [_jsxs("div", { className: "relative", children: [_jsx(Input, { type: showPassword ? "text" : "password", placeholder: "Password", value: password, onChange: (e) => setPassword(e.target.value), className: "pr-10" }), _jsx("button", { type: "button", className: "absolute inset-y-0 right-0 pr-3 flex items-center", onClick: () => setShowPassword(!showPassword), children: showPassword ? (_jsx(EyeOff, { className: "h-4 w-4 text-gray-400 hover:text-gray-600" })) : (_jsx(Eye, { className: "h-4 w-4 text-gray-400 hover:text-gray-600" })) })] }), _jsxs("div", { className: "relative", children: [_jsx(Input, { type: showConfirmPassword ? "text" : "password", placeholder: "Confirm Password", value: confirmPassword, onChange: (e) => setConfirmPassword(e.target.value), className: "pr-10" }), _jsx("button", { type: "button", className: "absolute inset-y-0 right-0 pr-3 flex items-center", onClick: () => setShowConfirmPassword(!showConfirmPassword), children: showConfirmPassword ? (_jsx(EyeOff, { className: "h-4 w-4 text-gray-400 hover:text-gray-600" })) : (_jsx(Eye, { className: "h-4 w-4 text-gray-400 hover:text-gray-600" })) })] }), error && _jsx("p", { className: "text-sm text-red-600", children: error })] }), _jsx(DialogFooter, { children: _jsx(Button, { onClick: handleSubmit, children: "Change Password" }) })] }) }));
};
export default ResetPasswordDialog;
