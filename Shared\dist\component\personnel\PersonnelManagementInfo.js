import { jsx as _jsx, jsxs as _jsxs } from "react/jsx-runtime";
import { useState, useEffect } from "react";
import { Card, CardContent, CardHeader } from "../ui/card";
import { Button } from "../ui/button";
import { Input } from "../ui/input";
import { Select, SelectContent, SelectItem, SelectTrigger, SelectValue, } from "../ui/select";
import { Plus, User, Loader2, Trash2, Lock } from "lucide-react";
import { usePersonnel, useDeletePersonnel, useResetPassword, } from "../../hooks/usePersonnel";
import AddPersonnelModal from "./AddPersonnelModal";
import { useOrganizations } from "../../hooks/useOrganizations";
import { ApplicationName, } from "../../lib/Schemas/Personnel";
import ChangePasswordDialog from "./ChangePasswordDialog";
import { toast } from "../ui/use-toast";
import { useDecodedJwt } from "../../hooks/useDecodedJwt";
import { hasPermission } from "../../lib/permissionUtils";
import { setApiUrl } from "../../lib/api";
import ConfirmDialog from "../confirmation/ConfirmDialog";
import { useOrgPermissions } from "../../hooks/useMaster";
const PersonnelManagementInfo = ({ appName, selectedOrganizationId, onOrganizationChange, apiUrl, orgApiUrl, setPersonnel, }) => {
    const [searchTerm, setSearchTerm] = useState("");
    const [localSelectedOrg, setLocalSelectedOrg] = useState(selectedOrganizationId);
    const [modalOpen, setModalOpen] = useState(false);
    const [modalMode, setModalMode] = useState("add");
    const [selectedPerson, setSelectedPerson] = useState(null);
    const [resetDialogOpen, setResetDialogOpen] = useState(false);
    const [resetPersonId, setResetPersonId] = useState(null);
    const [confirmOpen, setConfirmOpen] = useState(false);
    const [personnelToDelete, setPersonnelToDelete] = useState(null);
    // Set API URL if provided from parent app
    useEffect(() => {
        if (apiUrl) {
            setApiUrl(apiUrl);
        }
    }, [apiUrl]);
    const { data: personnel = [], isLoading, error, refetch } = usePersonnel();
    const { data: apiOrganizations } = useOrganizations();
    const { data: permissionList = [] } = useOrgPermissions(apiUrl || "");
    const deletePersonnelMutation = useDeletePersonnel();
    const resetPasswordMutation = useResetPassword();
    const jwtPayload = useDecodedJwt();
    const permissions = jwtPayload?.permissionList || [];
    if (personnel?.data?.length > 0 && setPersonnel) {
        setPersonnel(personnel.data);
    }
    // Check permissions
    const canViewPersonnel = hasPermission(permissions, "personnel", "read");
    const canAddPersonnel = hasPermission(permissions, "personnel", "create");
    const canEditPersonnel = hasPermission(permissions, "personnel", "update");
    const canDeletePersonnel = hasPermission(permissions, "personnel", "delete");
    // Debug logging for permission issues
    useEffect(() => { }, [
        jwtPayload,
        permissions,
        canViewPersonnel,
        canAddPersonnel,
        canEditPersonnel,
        canDeletePersonnel,
    ]);
    // Sync local state with prop
    useEffect(() => {
        setLocalSelectedOrg(selectedOrganizationId);
    }, [selectedOrganizationId]);
    // Handle organization change
    const handleOrganizationChange = (orgId) => {
        setLocalSelectedOrg(orgId);
        onOrganizationChange(orgId);
    };
    const organizations = Array.isArray(apiOrganizations)
        ? apiOrganizations.map((org) => ({
            id: org.id ?? org._id,
            name: org.organizationName,
        }))
        : [];
    const filteredPersonnel = personnel.data?.filter((person) => {
        // Convert both to strings for comparison to handle type mismatches
        const personOrgId = String(person.organizationId || "");
        const selectedOrgId = String(localSelectedOrg || "");
        const matchesOrg = selectedOrgId === "all" ||
            selectedOrgId === "" ||
            personOrgId === selectedOrgId;
        const matchesSearch = searchTerm.trim() === "" ||
            `${person?.firstName} ${person?.lastName}`
                .toLowerCase()
                .includes(searchTerm.toLowerCase()) ||
            person.email.toLowerCase().includes(searchTerm.toLowerCase());
        return matchesOrg && matchesSearch;
    });
    const handleDeletePersonnel = (id) => {
        setPersonnelToDelete(id);
        setConfirmOpen(true);
    };
    const handleConfirmDelete = () => {
        if (personnelToDelete) {
            deletePersonnelMutation.mutate(personnelToDelete);
            setConfirmOpen(false);
            setPersonnelToDelete(null);
        }
    };
    const handleEditPersonnel = (person) => {
        setSelectedPerson(person);
        setModalMode("edit");
        setModalOpen(true);
    };
    if (error) {
        return (_jsx("div", { className: "space-y-6", children: _jsxs("div", { className: "text-center py-8", children: [_jsx("p", { className: "text-red-600 mb-4", children: "Failed to load personnel data" }), _jsx(Button, { onClick: () => refetch(), children: "Retry" })] }) }));
    }
    const handleResetPassword = async ({ currentPassword, password, }) => {
        try {
            if (!resetPersonId)
                return;
            await resetPasswordMutation.mutateAsync({
                id: resetPersonId,
                currentPassword,
                password,
            });
            toast({ title: "Success", description: "Password reset successfully" });
        }
        catch (error) {
            toast({
                title: "Error",
                description: error.message || "Password reset failed",
                variant: "destructive",
            });
        }
    };
    return (_jsxs("div", { className: "space-y-6", style: { padding: "20px" }, children: [_jsxs("div", { className: "flex justify-between items-center", children: [_jsxs("div", { children: [_jsx("h2", { className: "text-3xl font-bold text-foreground", children: "Personnel Management" }), _jsx("p", { className: "text-muted-foreground mt-2", children: "Manage personnel records and user access" })] }), canAddPersonnel && (_jsxs(Button, { className: "bg-primary hover:bg-primary/90 text-primary-foreground", onClick: () => {
                            setModalMode("add");
                            setSelectedPerson(null);
                            setModalOpen(true);
                        }, children: [_jsx(Plus, { className: "mr-2 h-4 w-4" }), "Add Personnel"] }))] }), !canViewPersonnel ? (_jsx("div", { className: "flex items-center justify-center h-[50vh] text-lg text-muted-foreground", children: "You do not have permission to view personnel." })) : (_jsxs(Card, { children: [_jsx(CardHeader, { children: _jsxs("div", { className: "flex flex-col sm:flex-row gap-4", children: [_jsx("div", { className: "flex-1", children: _jsx(Input, { placeholder: "Search personnel...", value: searchTerm, onChange: (e) => setSearchTerm(e.target.value) }) }), appName === ApplicationName.RELATIVITY_ADMIN && (_jsxs(Select, { value: localSelectedOrg, onValueChange: handleOrganizationChange, children: [_jsx(SelectTrigger, { className: "w-full sm:w-48", children: _jsx(SelectValue, { placeholder: "Filter by organization" }) }), _jsxs(SelectContent, { children: [_jsx(SelectItem, { value: "all", children: "All Organizations" }), organizations.map((org) => (_jsx(SelectItem, { value: org.id, children: org.name }, org.id)))] })] }))] }) }), _jsx(CardContent, { children: isLoading ? (_jsx("div", { className: "flex justify-center py-8", children: _jsx(Loader2, { className: "h-8 w-8 animate-spin text-primary" }) })) : (_jsxs("div", { className: "space-y-4", children: [filteredPersonnel.map((person) => (_jsx("div", { className: "border border-border rounded-lg p-4 hover:bg-muted/50 transition-colors", children: _jsxs("div", { className: "flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 border-b border-border pb-2 mb-2", children: [_jsxs("div", { className: "flex items-center gap-4 flex-1 min-w-0", children: [_jsx("div", { className: "bg-primary/10 p-2 rounded-lg", children: _jsx(User, { className: "h-6 w-6 text-primary" }) }), _jsxs("div", { className: "min-w-0", children: [_jsxs("div", { className: "font-semibold text-lg text-foreground truncate", children: [person.firstName, " ", person.lastName] }), _jsx("div", { className: "text-sm text-muted-foreground truncate", children: person.email }), _jsx("div", { className: "mt-1", children: _jsxs("span", { className: "block font-semibold text-base text-muted-foreground", children: ["Org: ", person.organizationName || "—"] }) })] })] }), _jsxs("div", { className: "flex flex-col items-end gap-1 min-w-[120px]", children: [_jsxs("span", { className: "text-xs text-muted-foreground", children: ["Created:", " ", person.createdAt
                                                                ? new Date(person.createdAt).toLocaleDateString()
                                                                : "—"] }), _jsxs("span", { className: "text-xs text-muted-foreground", children: ["Updated:", " ", person.updatedAt
                                                                ? new Date(person.updatedAt).toLocaleDateString()
                                                                : "—"] })] }), _jsxs("div", { className: "flex gap-2 mt-2 sm:mt-0", children: [canEditPersonnel && (_jsx(Button, { variant: "outline", size: "sm", onClick: () => handleEditPersonnel(person), children: "Edit" })), canDeletePersonnel && (_jsx(Button, { variant: "outline", size: "sm", onClick: () => handleDeletePersonnel(person.id), disabled: deletePersonnelMutation.isPending, className: "text-destructive hover:text-destructive-foreground", children: deletePersonnelMutation.isPending ? (_jsx(Loader2, { className: "h-4 w-4 animate-spin" })) : (_jsx(Trash2, { className: "h-4 w-4" })) })), _jsx(Button, { variant: "outline", size: "sm", onClick: () => {
                                                            setResetPersonId(person.id);
                                                            setResetDialogOpen(true);
                                                        }, children: _jsx(Lock, {}) })] })] }) }, person.id))), filteredPersonnel.length === 0 && !isLoading && (_jsx("div", { className: "text-center py-8 text-muted-foreground", children: "No personnel found matching your criteria" }))] })) })] })), _jsx(AddPersonnelModal, { open: modalOpen, onOpenChange: setModalOpen, mode: modalMode, person: selectedPerson, licenseInfo: personnel?.licenseInfo, selectedOrganizationId: selectedOrganizationId, appName: appName, orgApiUrl: orgApiUrl, permissionList: permissionList }), _jsx(ChangePasswordDialog, { open: resetDialogOpen, onOpenChange: setResetDialogOpen, onSubmit: handleResetPassword }), _jsx(ConfirmDialog, { open: confirmOpen, onCancel: () => setConfirmOpen(false), onConfirm: handleConfirmDelete, title: "Delete Personnel", description: "Are you sure you want to delete this personnel? This action cannot be undone.", confirmText: "Yes, Delete", cancelText: "Cancel" })] }));
};
export default PersonnelManagementInfo;
