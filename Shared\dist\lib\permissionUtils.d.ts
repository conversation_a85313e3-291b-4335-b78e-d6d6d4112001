export interface Permission {
    _id: string;
    resource: string;
    action: string;
    description: string;
    createdAt: string;
    showGlobal: boolean;
}
/**
 * Check if user has a specific permission
 * @param permissions - Array of permission objects or strings
 * @param resource - Resource name (e.g., 'personnel')
 * @param action - Action name (e.g., 'read', 'create', 'update', 'delete')
 * @returns boolean indicating if permission exists
 */
export declare const hasPermission: (permissions: Permission[] | string[] | Record<string, string[]>, resource: string, action: string) => boolean;
/**
 * Check if user has any of the specified permissions
 * @param permissions - Array of permission strings
 * @param requiredPermissions - Array of required permissions
 * @returns boolean indicating if any permission exists
 */
export declare const hasAnyPermission: (permissions: string[], requiredPermissions: string[]) => boolean;
/**
 * Check if user has all of the specified permissions
 * @param permissions - Array of permission strings
 * @param requiredPermissions - Array of required permissions
 * @returns boolean indicating if all permissions exist
 */
export declare const hasAllPermissions: (permissions: string[], requiredPermissions: string[]) => boolean;
