import React from "react";
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON>ooter,
} from "../ui/dialog";
import { <PERSON><PERSON> } from "../ui/button";
import { Input } from "../ui/input";
import { Label } from "../ui/label";
import { Textarea } from "../ui/textarea";
import { Checkbox } from "../ui/checkbox";
import { Card, CardContent, CardHeader, CardTitle } from "../ui/card";
import { Save, X } from "lucide-react";

import { Permission } from "../../lib/permissionUtils";

export interface AddEditRoleModalProps {
  open: boolean;
  onClose: () => void;
  role: {
    id?: string;
    name?: string;
    description?: string;
    permissionsList?: Permission[];
  };
  setRole: (role: any) => void;
  isEdit: boolean;
  onSave: () => void;
  availablePermissions: Permission[];
}

const AddEditRoleModal: React.FC<AddEditRoleModalProps> = ({
  open,
  onClose,
  role,
  setRole,
  isEdit,
  onSave,
  availablePermissions,
}) => {
  const groupedPermissions = availablePermissions.reduce((acc, permission) => {
    if (!acc[permission.resource]) acc[permission.resource] = [];
    acc[permission.resource].push(permission);
    return acc;
  }, {} as Record<string, typeof availablePermissions>);

  const handlePermissionToggle = (permissionId: string) => {
    const currentPermissions = role.permissionsList || [];
    const exists = currentPermissions.some((p) => p._id === permissionId);

    let newPermissionsList;
    if (exists) {
      // Remove the permission object
      newPermissionsList = currentPermissions.filter(
        (p) => p._id !== permissionId
      );
    } else {
      // Add the permission object
      const permissionToAdd = availablePermissions.find(
        (p) => p._id === permissionId
      );
      if (permissionToAdd) {
        newPermissionsList = [...currentPermissions, permissionToAdd];
      } else {
        newPermissionsList = currentPermissions;
      }
    }

    setRole({ ...role, permissionsList: newPermissionsList });
  };

  const handleSave = () => {
    onSave();
  };

  return (
    <Dialog open={open} onOpenChange={onClose}>
      <DialogContent
        className="p-0 gap-0 overflow-hidden"
        style={{
          maxWidth: "90vw",
          width: "70%",
          maxHeight: "90vh",
          height: "100%",
          minHeight: "600px",
        }}>
        <div
          style={{ display: "flex", flexDirection: "column", height: "100%" }}>
          <DialogHeader
            className="px-6 py-4 border-b"
            style={{ flexShrink: 0 }}>
            <DialogTitle className="text-lg font-semibold">
              {isEdit ? "Edit Role" : "Create Role"}
            </DialogTitle>
          </DialogHeader>

          <div
            className="px-6 py-4"
            style={{
              flex: 1,
              overflowY: "auto",
              //minHeight: 0,
              //maxHeight: 'calc(90vh - 140px)'
            }}>
            <div
              style={{ display: "flex", flexDirection: "column", gap: "24px" }}>
              <div>
                <Label htmlFor="roleName" className="text-sm font-medium">
                  Role Name
                </Label>
                <Input
                  id="roleName"
                  value={role.name || ""}
                  onChange={(e) => setRole({ ...role, name: e.target.value })}
                  placeholder="Enter role name"
                  className="mt-1"
                />
              </div>

              <div>
                <Label
                  htmlFor="roleDescription"
                  className="text-sm font-medium">
                  Description
                </Label>
                <Textarea
                  id="roleDescription"
                  value={role.description || ""}
                  onChange={(e) =>
                    setRole({ ...role, description: e.target.value })
                  }
                  placeholder="Enter role description"
                  rows={2}
                  className="mt-1"
                />
              </div>

              <div>
                <h3 className="text-lg font-semibold mb-4">Permissions</h3>
                <div
                  style={{
                    maxHeight: "300px",
                    overflowY: "auto",
                    paddingRight: "8px",
                    display: "flex",
                    flexDirection: "column",
                    gap: "16px",
                  }}>
                  {Object.entries(groupedPermissions).map(
                    ([resource, perms]) => (
                      <Card key={resource} className="border shadow-sm">
                        <CardHeader className="pb-3 pt-4 px-5">
                          <CardTitle className="text-base font-semibold capitalize">
                            {resource}
                          </CardTitle>
                        </CardHeader>
                        <CardContent className="pt-0 px-5 pb-4">
                          <div
                            style={{
                              display: "grid",
                              gridTemplateColumns:
                                "repeat(auto-fit, minmax(250px, 1fr))",
                              gap: "12px",
                            }}>
                            {perms?.map((perm) => (
                              <div
                                key={perm._id}
                                className="flex items-start space-x-3 py-2 px-2 rounded hover:bg-background-50"
                                style={{ minHeight: "40px" }}>
                                <Checkbox
                                  id={perm._id}
                                  checked={
                                    role.permissionsList?.some(
                                      (p) => p._id === perm._id
                                    ) || false
                                  }
                                  onCheckedChange={() =>
                                    handlePermissionToggle(perm._id)
                                  }
                                  className="mt-1 flex-shrink-0"
                                />
                                <Label
                                  htmlFor={perm._id}
                                  className="text-sm font-normal cursor-pointer leading-relaxed break-words"
                                  style={{ wordBreak: "break-word" }}>
                                  {perm.description}
                                </Label>
                              </div>
                            ))}
                          </div>
                        </CardContent>
                      </Card>
                    )
                  )}
                </div>
              </div>
            </div>
          </div>

          <DialogFooter
            className="px-6 py-4 border-t bg-background-50"
            style={{
              flexShrink: 0,
              position: "sticky",
              bottom: 0,
              zIndex: 10,
            }}>
            <div className="flex gap-3 w-full justify-end">
              <Button
                variant="outline"
                onClick={onClose}
                className="min-w-[100px]">
                <X className="w-4 h-4 mr-2" />
                Cancel
              </Button>
              <Button onClick={handleSave} className="min-w-[120px]">
                <Save className="w-4 h-4 mr-2" />
                {isEdit ? "Update Role" : "Create Role"}
              </Button>
            </div>
          </DialogFooter>
        </div>
      </DialogContent>
    </Dialog>
  );
};

export default AddEditRoleModal;
