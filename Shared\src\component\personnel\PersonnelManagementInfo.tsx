import React, { useState, useEffect } from "react";
import { <PERSON>, CardContent, CardHeader } from "../ui/card";
import { Button } from "../ui/button";
import { Input } from "../ui/input";

import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "../ui/select";

import { Plus, User, Loader2, Trash2, Lock } from "lucide-react";
import {
  usePersonnel,
  useDeletePersonnel,
  useResetPassword,
} from "../../hooks/usePersonnel";
import AddPersonnelModal from "./AddPersonnelModal";
import { useOrganizations } from "../../hooks/useOrganizations";
import {
  ApplicationName,
  type Person,
  type ResetPassword,
} from "../../lib/Schemas/Personnel";
import ChangePasswordDialog from "./ChangePasswordDialog";
import { toast } from "../ui/use-toast";
import { useDecodedJwt } from "../../hooks/useDecodedJwt";
import { hasPermission } from "../../lib/permissionUtils";

import { setApiUrl } from "../../lib/api";
import ConfirmDialog from "../confirmation/ConfirmDialog";
import { useOrgPermissions } from "../../hooks/useMaster";

interface PersonnelManagementInfoProps {
  appName: string;
  selectedOrganizationId: string;
  onOrganizationChange: (organizationId: string) => void;
  apiUrl?: string; // Optional API URL from parent app
  orgApiUrl?: string;
  setPersonnel?: (personList: any) => void;
}

const PersonnelManagementInfo: React.FC<PersonnelManagementInfoProps> = ({
  appName,
  selectedOrganizationId,
  onOrganizationChange,
  apiUrl,
  orgApiUrl,
  setPersonnel,
}) => {
  const [searchTerm, setSearchTerm] = useState("");
  const [localSelectedOrg, setLocalSelectedOrg] = useState(
    selectedOrganizationId
  );
  const [modalOpen, setModalOpen] = useState(false);
  const [modalMode, setModalMode] = useState<"add" | "edit">("add");
  const [selectedPerson, setSelectedPerson] = useState<Person | null>(null);
  const [resetDialogOpen, setResetDialogOpen] = useState(false);
  const [resetPersonId, setResetPersonId] = useState<string | null>(null);
  const [confirmOpen, setConfirmOpen] = useState(false);
  const [personnelToDelete, setPersonnelToDelete] = useState<string | null>(
    null
  );

  // Set API URL if provided from parent app
  useEffect(() => {
    if (apiUrl) {
      setApiUrl(apiUrl);
    }
  }, [apiUrl]);

  const { data: personnel = [], isLoading, error, refetch } = usePersonnel();
  const { data: apiOrganizations } = useOrganizations();
  const { data: permissionList = [] } = useOrgPermissions(apiUrl || "");

  const deletePersonnelMutation = useDeletePersonnel();
  const resetPasswordMutation = useResetPassword();
  const jwtPayload = useDecodedJwt();
  const permissions = jwtPayload?.permissionList || [];

  if (personnel?.data?.length > 0 && setPersonnel) {
    setPersonnel(personnel.data);
  }

  // Check permissions
  const canViewPersonnel = hasPermission(permissions, "personnel", "read");
  const canAddPersonnel = hasPermission(permissions, "personnel", "create");
  const canEditPersonnel = hasPermission(permissions, "personnel", "update");
  const canDeletePersonnel = hasPermission(permissions, "personnel", "delete");

  // Debug logging for permission issues
  useEffect(() => {}, [
    jwtPayload,
    permissions,
    canViewPersonnel,
    canAddPersonnel,
    canEditPersonnel,
    canDeletePersonnel,
  ]);

  // Sync local state with prop
  useEffect(() => {
    setLocalSelectedOrg(selectedOrganizationId);
  }, [selectedOrganizationId]);

  // Handle organization change
  const handleOrganizationChange = (orgId: string) => {
    setLocalSelectedOrg(orgId);
    onOrganizationChange(orgId);
  };

  const organizations: { id: string; name: string }[] = Array.isArray(
    apiOrganizations
  )
    ? apiOrganizations.map((org: any) => ({
        id: org.id ?? org._id,
        name: org.organizationName,
      }))
    : [];

  const filteredPersonnel = personnel.data?.filter((person: Person) => {
    // Convert both to strings for comparison to handle type mismatches
    const personOrgId = String(person.organizationId || "");
    const selectedOrgId = String(localSelectedOrg || "");

    const matchesOrg =
      selectedOrgId === "all" ||
      selectedOrgId === "" ||
      personOrgId === selectedOrgId;

    const matchesSearch =
      searchTerm.trim() === "" ||
      `${person?.firstName} ${person?.lastName}`
        .toLowerCase()
        .includes(searchTerm.toLowerCase()) ||
      person.email.toLowerCase().includes(searchTerm.toLowerCase());

    return matchesOrg && matchesSearch;
  });

  const handleDeletePersonnel = (id: string) => {
    setPersonnelToDelete(id);
    setConfirmOpen(true);
  };

  const handleConfirmDelete = () => {
    if (personnelToDelete) {
      deletePersonnelMutation.mutate(personnelToDelete);
      setConfirmOpen(false);
      setPersonnelToDelete(null);
    }
  };

  const handleEditPersonnel = (person: Person) => {
    setSelectedPerson(person);
    setModalMode("edit");
    setModalOpen(true);
  };

  if (error) {
    return (
      <div className="space-y-6">
        <div className="text-center py-8">
          <p className="text-red-600 mb-4">Failed to load personnel data</p>
          <Button onClick={() => refetch()}>Retry</Button>
        </div>
      </div>
    );
  }

  const handleResetPassword = async ({
    currentPassword,
    password,
  }: ResetPassword) => {
    try {
      if (!resetPersonId) return;
      await resetPasswordMutation.mutateAsync({
        id: resetPersonId,
        currentPassword,
        password,
      });
      toast({ title: "Success", description: "Password reset successfully" });
    } catch (error: any) {
      toast({
        title: "Error",
        description: error.message || "Password reset failed",
        variant: "destructive",
      });
    }
  };

  // Extend Person type locally to include createdAt and updatedAt for display
  // This does not affect the backend, just the local typing for this component

  type PersonWithDates = Person & {
    createdAt?: string;
    updatedAt?: string;
  };

  return (
    <div className="space-y-6" style={{ padding: "20px" }}>
      <div className="flex justify-between items-center">
        <div>
          <h2 className="text-3xl font-bold text-foreground">
            Personnel Management
          </h2>
          <p className="text-muted-foreground mt-2">
            Manage personnel records and user access
          </p>
        </div>
        {canAddPersonnel && (
          <Button
            className="bg-primary hover:bg-primary/90 text-primary-foreground"
            onClick={() => {
              setModalMode("add");
              setSelectedPerson(null);
              setModalOpen(true);
            }}>
            <Plus className="mr-2 h-4 w-4" />
            Add Personnel
          </Button>
        )}
      </div>

      {!canViewPersonnel ? (
        <div className="flex items-center justify-center h-[50vh] text-lg text-muted-foreground">
          You do not have permission to view personnel.
        </div>
      ) : (
        <Card>
          <CardHeader>
            <div className="flex flex-col sm:flex-row gap-4">
              <div className="flex-1">
                <Input
                  placeholder="Search personnel..."
                  value={searchTerm}
                  onChange={(e) => setSearchTerm(e.target.value)}
                />
              </div>
              {appName === ApplicationName.RELATIVITY_ADMIN && (
                <Select
                  value={localSelectedOrg}
                  onValueChange={handleOrganizationChange}>
                  <SelectTrigger className="w-full sm:w-48">
                    <SelectValue placeholder="Filter by organization" />
                  </SelectTrigger>
                  <SelectContent>
                    <SelectItem value="all">All Organizations</SelectItem>
                    {organizations.map((org) => (
                      <SelectItem key={org.id} value={org.id}>
                        {org.name}
                      </SelectItem>
                    ))}
                  </SelectContent>
                </Select>
              )}
            </div>
          </CardHeader>
          <CardContent>
            {isLoading ? (
              <div className="flex justify-center py-8">
                <Loader2 className="h-8 w-8 animate-spin text-primary" />
              </div>
            ) : (
              <div className="space-y-4">
                {filteredPersonnel.map((person: PersonWithDates) => (
                  <div
                    key={person.id}
                    className="border border-border rounded-lg p-4 hover:bg-muted/50 transition-colors">
                    <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-4 border-b border-border pb-2 mb-2">
                      <div className="flex items-center gap-4 flex-1 min-w-0">
                        <div className="bg-primary/10 p-2 rounded-lg">
                          <User className="h-6 w-6 text-primary" />
                        </div>
                        <div className="min-w-0">
                          <div className="font-semibold text-lg text-foreground truncate">
                            {person.firstName} {person.lastName}
                          </div>
                          <div className="text-sm text-muted-foreground truncate">
                            {person.email}
                          </div>
                          <div className="mt-1">
                            <span className="block font-semibold text-base text-muted-foreground">
                              Org: {person.organizationName || "—"}
                            </span>
                          </div>
                        </div>
                      </div>
                      {/* Center column for Apps only */}
                      {/* <div className="flex flex-col items-start justify-center flex-1 min-w-0 sm:items-center">
                        <div className="text-xs text-muted-foreground truncate"><span className="font-semibold">Apps:</span> {Array.isArray(person.allowedApplications) && person.allowedApplications.length > 0 ? person.allowedApplications.join(", ") : "—"}</div>
                      </div> */}
                      <div className="flex flex-col items-end gap-1 min-w-[120px]">
                        <span className="text-xs text-muted-foreground">
                          Created:{" "}
                          {person.createdAt
                            ? new Date(person.createdAt).toLocaleDateString()
                            : "—"}
                        </span>
                        <span className="text-xs text-muted-foreground">
                          Updated:{" "}
                          {person.updatedAt
                            ? new Date(person.updatedAt).toLocaleDateString()
                            : "—"}
                        </span>
                      </div>
                      <div className="flex gap-2 mt-2 sm:mt-0">
                        {canEditPersonnel && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleEditPersonnel(person)}>
                            Edit
                          </Button>
                        )}
                        {canDeletePersonnel && (
                          <Button
                            variant="outline"
                            size="sm"
                            onClick={() => handleDeletePersonnel(person.id)}
                            disabled={deletePersonnelMutation.isPending}
                            className="text-destructive hover:text-destructive-foreground">
                            {deletePersonnelMutation.isPending ? (
                              <Loader2 className="h-4 w-4 animate-spin" />
                            ) : (
                              <Trash2 className="h-4 w-4" />
                            )}
                          </Button>
                        )}
                        <Button
                          variant="outline"
                          size="sm"
                          onClick={() => {
                            setResetPersonId(person.id);
                            setResetDialogOpen(true);
                          }}>
                          <Lock />
                        </Button>
                      </div>
                    </div>
                  </div>
                ))}
                {filteredPersonnel.length === 0 && !isLoading && (
                  <div className="text-center py-8 text-muted-foreground">
                    No personnel found matching your criteria
                  </div>
                )}
              </div>
            )}
          </CardContent>
        </Card>
      )}

      <AddPersonnelModal
        open={modalOpen}
        onOpenChange={setModalOpen}
        mode={modalMode}
        person={selectedPerson}
        licenseInfo={personnel?.licenseInfo}
        selectedOrganizationId={selectedOrganizationId}
        appName={appName}
        orgApiUrl={orgApiUrl}
        permissionList={permissionList}
      />

      <ChangePasswordDialog
        open={resetDialogOpen}
        onOpenChange={setResetDialogOpen}
        onSubmit={handleResetPassword}
      />

      <ConfirmDialog
        open={confirmOpen}
        onCancel={() => setConfirmOpen(false)}
        onConfirm={handleConfirmDelete}
        title="Delete Personnel"
        description="Are you sure you want to delete this personnel? This action cannot be undone."
        confirmText="Yes, Delete"
        cancelText="Cancel"
      />
    </div>
  );
};

export default PersonnelManagementInfo;
