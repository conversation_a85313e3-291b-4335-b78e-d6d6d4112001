import { type LanguageConfig, type Language } from './LanguageProvider';

// Common language sets
export const languages: Language[] = [
  { code: 'en', name: 'English', nativeName: 'English', flag: 'us' },
  { code: 'es', name: 'Spanish', nativeName: 'Spanish', flag: 'es' },
];


// Standard React configuration
export const reactLanguageConfig: LanguageConfig = {
  defaultLanguage: 'en',
  storageKey: 'app-language',
  languages: languages,
  fallbackLanguage: 'en',
  detectBrowserLanguage: true,
};


// Custom configuration builder
export function createLanguageConfig(overrides: Partial<LanguageConfig> = {}): LanguageConfig {
  return {
    ...reactLanguageConfig,
    ...overrides,
  };
}

// Sample translations for common UI elements
export const commonTranslations = {
  en: {
    'header.title': 'Relativity Cloud Admin',
    'language.select': 'Select Language',
    'language.current': 'Current Language',
    'theme.select': 'Select Theme',
    'theme.light': 'Light',
    'theme.dark': 'Dark',
    'theme.system': 'System',
    'common.save': 'Save',
    'common.cancel': 'Cancel',
    'common.apply': 'Apply',
    'common.close': 'Close',
    'common.settings': 'Settings',
    'common.preferences': 'Preferences',
    'nav.home': 'Home',
    'nav.about': 'About',
    'nav.contact': 'Contact',
    'nav.help': 'Help',
    'nav.dashboard': 'Dashboard',
    'nav.personnel': 'Personnel',
    'nav.organizations': 'Organizations',
    'nav.roles': 'Roles',
  },
  es: {
    'header.title': 'Relativity Cloud Admin es',
    'language.select': 'Seleccionar Idioma',
    'language.current': 'Idioma Actual',
    'theme.select': 'Seleccionar Tema',
    'theme.light': 'Claro',
    'theme.dark': 'Oscuro',
    'theme.system': 'Sistema',
    'common.save': 'Guardar',
    'common.cancel': 'Cancelar',
    'common.apply': 'Aplicar',
    'common.close': 'Cerrar',
    'common.settings': 'Configuración',
    'common.preferences': 'Preferencias',
    'nav.home': 'Inicio',
    'nav.about': 'Acerca de',
    'nav.contact': 'Contacto',
    'nav.help': 'Ayuda',
    'nav.dashboard': 'Dashboard es',
    'nav.personnel': 'Personnel es',
    'nav.organizations': 'Organizations es',
    'nav.roles': 'Roles es',
  },
};

export default {
  languages,
  createLanguageConfig,
  commonTranslations,
};
