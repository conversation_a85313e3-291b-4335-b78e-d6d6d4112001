// Personnel schemas and types
// These should match your actual data models

export enum ApplicationName {
  RELATIVITY_ADMIN = "relativityadmin",
  ORGANIZATION_ADMIN = "organizationadmin",
}

export interface Person {
  id: string;
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  organizationId: string;
  organizationName?: string;
  roles: string[] | any[]; // Support both string IDs and role objects
  roleName?: string;
  hasLogin: boolean;
  status: string;
  allowedApplications?: string[];
  selectedPermissions?: string[];
  isSystemUser: boolean;
}

import { Permission } from "../permissionUtils";

export interface PersonnelRoles {
  id: string;
  _id?: string;
  name: string;
  description: string;
  permissionsList?: Permission[];
}

export interface ResetPassword {
  id?: string;
  currentPassword: string;
  password: string;
}

export interface CreatePersonnelRequest {
  firstName: string;
  lastName: string;
  email: string;
  phone: string;
  organizationId: string;
  roleId: string[] | PersonnelRoles[]; // Support both string IDs and role objects
  allowedApplications: string[];
  licenseInfo: any[];
  data: any[];
}

export interface UpdatePersonnelRequest extends Partial<Person> {
  id: string;
}
