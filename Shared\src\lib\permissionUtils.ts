// Permission utility functions
// These should be implemented based on your actual permission system

export interface Permission {
  _id: string;
  resource: string;
  action: string;
  description: string;
  createdAt: string;
  showGlobal: boolean;
}

/**
 * Check if user has a specific permission
 * @param permissions - Array of permission objects or strings
 * @param resource - Resource name (e.g., 'personnel')
 * @param action - Action name (e.g., 'read', 'create', 'update', 'delete')
 * @returns boolean indicating if permission exists
 */
export const hasPermission = (
  permissions: Permission[] | string[] | Record<string, string[]>,
  resource: string,
  action: string
): boolean => {
  if (!permissions) return false;

  // Handle permissionList object format
  if (typeof permissions === "object" && !Array.isArray(permissions)) {
    const permissionList = permissions as Record<string, string[]>;
    if (permissionList[resource] && permissionList[resource].includes(action)) {
      return true;
    }
    // Wildcard: if resource:* or *:action or *:*
    if (permissionList[resource] && permissionList[resource].includes("*")) {
      return true;
    }
    if (
      permissionList["*"] &&
      (permissionList["*"].includes(action) || permissionList["*"].includes("*"))
    ) {
      return true;
    }
    return false;
  }

  if (!Array.isArray(permissions) || permissions.length === 0) {
    return false;
  }

  // Handle object-based permissions (new format)
  if (
    permissions.length > 0 &&
    typeof permissions[0] === "object" &&
    permissions[0] !== null
  ) {
    const permissionObjects = permissions as Permission[];

    // Check for exact permission match
    const hasExactPermission = permissionObjects.some(
      (perm) => perm.resource === resource && perm.action === action
    );

    if (hasExactPermission) {
      return true;
    }

    // Check for wildcard permissions (Super Admin)
    const hasWildcardPermission = permissionObjects.some(
      (perm) =>
        (perm.resource === "*" && perm.action === "*") ||
        (perm.resource === resource && perm.action === "*") ||
        (perm.resource === "*" && perm.action === action)
    );

    if (hasWildcardPermission) {
      return true;
    }

    return false;
  }

  // Handle string-based permissions (legacy format)
  const stringPermissions = permissions as string[];

  // Check for exact permission match
  const exactPermission = `${resource}:${action}`;
  if (stringPermissions.includes(exactPermission)) {
    return true;
  }

  // Check for wildcard permissions
  const resourceWildcard = `${resource}:*`;
  if (stringPermissions.includes(resourceWildcard)) {
    return true;
  }

  // Check for admin permission
  if (
    stringPermissions.includes("*:*") ||
    stringPermissions.includes("admin")
  ) {
    return true;
  }

  // Additional checks for common permission formats
  if (
    stringPermissions.includes("superadmin") ||
    stringPermissions.includes("super_admin")
  ) {
    return true;
  }

  // Check if permissions contain any admin-like permissions
  const hasAdminPermission = stringPermissions.some(
    (perm) =>
      perm.toLowerCase().includes("admin") ||
      perm.includes("*") ||
      perm.toLowerCase().includes("all")
  );

  if (hasAdminPermission) {
    return true;
  }

  return false;
};

/**
 * Check if user has any of the specified permissions
 * @param permissions - Array of permission strings
 * @param requiredPermissions - Array of required permissions
 * @returns boolean indicating if any permission exists
 */
export const hasAnyPermission = (
  permissions: string[],
  requiredPermissions: string[]
): boolean => {
  return requiredPermissions.some((permission) => {
    const [resource, action] = permission.split(":");
    return hasPermission(permissions, resource, action);
  });
};

/**
 * Check if user has all of the specified permissions
 * @param permissions - Array of permission strings
 * @param requiredPermissions - Array of required permissions
 * @returns boolean indicating if all permissions exist
 */
export const hasAllPermissions = (
  permissions: string[],
  requiredPermissions: string[]
): boolean => {
  return requiredPermissions.every((permission) => {
    const [resource, action] = permission.split(":");
    return hasPermission(permissions, resource, action);
  });
};
